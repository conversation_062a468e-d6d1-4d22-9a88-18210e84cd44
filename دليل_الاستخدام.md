# دليل استخدام إضافة Q2 Web Scraper

## مقدمة
إضافة Q2 Web Scraper هي أداة قوية ومتقدمة لكشط البيانات من صفحات الويب بطريقة ذكية وسهلة الاستخدام. تم تصميم هذه الإضافة خصيصاً للمستخدمين العرب مع واجهة باللغة العربية ودعم كامل للنصوص العربية.

## المتطلبات
- متصفح Google Chrome الإصدار 88 أو أحدث
- اتصال بالإنترنت لتحميل البيانات
- مساحة تخزين كافية لحفظ البيانات المكشوطة

## التثبيت

### الطريقة الأولى: من Chrome Web Store (قريباً)
1. افتح Chrome Web Store
2. ابحث عن "Q2 Web Scraper"
3. انقر على "إضافة إلى Chrome"
4. أكد التثبيت

### الطريقة الثانية: التثبيت اليدوي
1. حمل ملفات الإضافة
2. افتح Chrome واذهب إلى `chrome://extensions/`
3. فعل "وضع المطور" (Developer mode)
4. انقر على "تحميل إضافة غير مُعبأة" (Load unpacked)
5. اختر مجلد الإضافة

## الواجهة الرئيسية

### التبويبات الأساسية
الإضافة تحتوي على أربعة تبويبات رئيسية:

#### 1. تبويب الجداول 🔍
- **الوظيفة**: كشط البيانات من الجداول الموجودة في الصفحة
- **الاستخدام**: 
  - انقر على "فحص الجداول في الصفحة"
  - اختر الجدول المطلوب من القائمة
  - حدد الأعمدة التي تريد كشطها
  - انقر على "استخراج البيانات"

#### 2. تبويب المنتجات 🛒
- **الوظيفة**: كشط معلومات المنتجات من مواقع التجارة الإلكترونية
- **الخيارات المتاحة**:
  - ✅ اسم المنتج
  - ✅ السعر
  - ⬜ صورة المنتج
  - ⬜ الوصف
  - ⬜ التقييم

#### 3. تبويب الصور 🖼️
- **الوظيفة**: كشط وتحميل الصور من المواقع
- **الإعدادات**:
  - الحد الأدنى لحجم الصورة (بكسل)
  - صيغ الصور المطلوبة (JPG, PNG, GIF, WebP)

#### 4. تبويب الإعدادات ⚙️
- **صيغة التصدير الافتراضية**: CSV, JSON, Excel
- **الحد الأقصى للنتائج**: 10-10000 عنصر
- **التحميل التلقائي**: تفعيل/إلغاء

## طرق الاستخدام

### 1. كشط الجداول

#### الخطوة الأولى: فحص الجداول
```
1. افتح الصفحة التي تحتوي على الجدول المطلوب
2. انقر على أيقونة Q2 في شريط الأدوات
3. اختر تبويب "الجداول"
4. انقر على "فحص الجداول في الصفحة"
```

#### الخطوة الثانية: اختيار الجدول
- ستظهر قائمة بجميع الجداول المكتشفة
- كل جدول يعرض عدد الصفوف والأعمدة
- انقر على "اختيار" للجدول المطلوب

#### الخطوة الثالثة: تحديد الأعمدة
- ستظهر قائمة بأعمدة الجدول
- حدد الأعمدة التي تريد كشطها
- انقر على "استخراج البيانات"

#### مثال عملي:
```
جدول يحتوي على معلومات الموظفين:
- العمود 1: الاسم ✅
- العمود 2: المنصب ✅  
- العمود 3: الراتب ⬜
- العمود 4: تاريخ التوظيف ✅
```

### 2. كشط المنتجات

#### المواقع المدعومة:
- أمازون (Amazon)
- إيباي (eBay)
- علي إكسبرس (AliExpress)
- سوق.كوم
- نون.كوم
- معظم مواقع التجارة الإلكترونية الأخرى

#### خطوات الكشط:
```
1. افتح صفحة المنتجات أو نتائج البحث
2. اختر تبويب "المنتجات"
3. حدد المعلومات المطلوبة:
   - اسم المنتج ✅ (مُوصى به)
   - السعر ✅ (مُوصى به)
   - صورة المنتج (اختياري)
   - الوصف (اختياري)
   - التقييم (اختياري)
4. انقر على "فحص المنتجات"
```

#### نصائح للحصول على أفضل النتائج:
- تأكد من تحميل الصفحة بالكامل قبل البدء
- في صفحات النتائج الطويلة، قم بالتمرير لأسفل لتحميل المزيد
- استخدم صفحات البحث بدلاً من الصفحة الرئيسية

### 3. كشط الصور

#### إعداد المعايير:
```
الحد الأدنى للحجم: 100 بكسل (افتراضي)
- للصور الصغيرة: 50-100 بكسل
- للصور المتوسطة: 200-500 بكسل  
- للصور عالية الجودة: 1000+ بكسل
```

#### صيغ الصور المدعومة:
- **JPG/JPEG**: الأكثر شيوعاً للصور
- **PNG**: للصور ذات الخلفية الشفافة
- **GIF**: للصور المتحركة
- **WebP**: صيغة حديثة عالية الجودة

#### خطوات الكشط:
```
1. افتح الصفحة التي تحتوي على الصور
2. اختر تبويب "الصور"
3. اضبط الحد الأدنى للحجم
4. اختر صيغ الصور المطلوبة
5. انقر على "فحص الصور"
```

## التصدير والحفظ

### صيغ التصدير المتاحة:

#### 1. CSV (Comma-Separated Values)
- **الاستخدام**: Excel, Google Sheets
- **المميزات**: سهولة الفتح والتعديل
- **مناسب لـ**: الجداول والبيانات المنظمة

#### 2. JSON (JavaScript Object Notation)
- **الاستخدام**: البرمجة والتطبيقات
- **المميزات**: يحافظ على هيكل البيانات المعقد
- **مناسب لـ**: التطوير والتحليل المتقدم

#### 3. Excel (قريباً)
- **الاستخدام**: Microsoft Excel
- **المميزات**: تنسيق متقدم وجداول منظمة
- **مناسب لـ**: التقارير والعروض التقديمية

### خطوات التصدير:
```
1. بعد كشط البيانات، ستظهر منطقة "النتائج"
2. انقر على "معاينة" لرؤية البيانات
3. انقر على "تحميل" لحفظ الملف
4. اختر موقع الحفظ واسم الملف
```

## الميزات المتقدمة

### 1. قوائم السياق (Context Menus)
انقر بالزر الأيمن على أي عنصر في الصفحة:
- **"كشط هذا الجدول"**: لكشط جدول محدد
- **"حفظ هذه الصورة"**: لتحميل صورة واحدة
- **"كشط بيانات الصفحة"**: لفتح الأدوات الكاملة

### 2. التمييز البصري
الإضافة تميز العناصر المكتشفة بألوان مختلفة:
- 🟢 **أخضر**: الجداول المكتشفة
- 🔵 **أزرق**: الجدول المحدد حالياً
- 🟠 **برتقالي**: المنتجات المكتشفة
- 🔴 **وردي**: الصور المكتشفة

### 3. الإشعارات
تظهر إشعارات لإعلامك بـ:
- نجاح العمليات
- الأخطاء والمشاكل
- اكتمال التحميل

## استكشاف الأخطاء وحلها

### المشاكل الشائعة:

#### 1. "لم يتم العثور على جداول"
**الأسباب المحتملة:**
- الصفحة لا تحتوي على جداول HTML
- الجداول مخفية أو محملة ديناميكياً
- الجداول صغيرة جداً (أقل من صفين)

**الحلول:**
- تأكد من تحميل الصفحة بالكامل
- جرب التمرير في الصفحة
- ابحث عن جداول في أقسام أخرى من الصفحة

#### 2. "فشل في استخراج البيانات"
**الأسباب المحتملة:**
- تغيير في هيكل الصفحة أثناء الكشط
- مشاكل في الاتصال بالإنترنت
- حماية من موقع الويب

**الحلول:**
- أعد تحميل الصفحة وحاول مرة أخرى
- تأكد من استقرار الاتصال
- جرب صفحة أخرى للاختبار

#### 3. "الإضافة لا تعمل على هذه الصفحة"
**الصفحات غير المدعومة:**
- صفحات Chrome الداخلية (chrome://)
- صفحات الإضافات (chrome-extension://)
- بعض المواقع المحمية

**الحل:**
- استخدم الإضافة على مواقع ويب عادية (http/https)

### نصائح للحصول على أفضل النتائج:

#### 1. للجداول:
- اختر الجداول التي تحتوي على بيانات منظمة
- تجنب الجداول المستخدمة للتخطيط فقط
- تأكد من وجود رؤوس واضحة للأعمدة

#### 2. للمنتجات:
- استخدم صفحات نتائج البحث
- تأكد من تحميل جميع المنتجات في الصفحة
- جرب مواقع تجارة إلكترونية مختلفة

#### 3. للصور:
- اضبط الحد الأدنى للحجم حسب احتياجاتك
- اختر صيغ الصور المناسبة لاستخدامك
- تجنب الصور الزخرفية الصغيرة

## الأمان والخصوصية

### ما تجمعه الإضافة:
- **لا شيء**: الإضافة لا ترسل أي بيانات لخوادم خارجية
- جميع العمليات تتم محلياً في متصفحك
- البيانات المكشوطة تُحفظ على جهازك فقط

### الصلاحيات المطلوبة:
- **activeTab**: للوصول للصفحة النشطة فقط
- **storage**: لحفظ الإعدادات محلياً
- **downloads**: لتحميل البيانات المكشوطة
- **scripting**: لحقن أكواد الكشط في الصفحات

### نصائح الأمان:
- لا تكشط بيانات حساسة أو شخصية
- احترم شروط استخدام المواقع
- استخدم البيانات المكشوطة بمسؤولية

## الدعم والمساعدة

### طرق الحصول على المساعدة:
1. **دليل الاستخدام**: هذا الملف
2. **الأسئلة الشائعة**: في ملف FAQ.md
3. **التقارير**: لإبلاغ عن مشاكل أو اقتراحات

### معلومات الاتصال:
- **البريد الإلكتروني**: <EMAIL>
- **الموقع الإلكتروني**: www.q2scraper.com
- **التحديثات**: تابع الإضافة للحصول على آخر التحديثات

## التحديثات المستقبلية

### الميزات القادمة:
- 📊 تصدير Excel متقدم
- 🤖 كشط ذكي بالذكاء الاصطناعي
- 📱 دعم المواقع المتجاوبة
- 🔄 جدولة الكشط التلقائي
- 📈 تحليل البيانات المدمج

### كيفية التحديث:
- التحديثات التلقائية من Chrome Web Store
- إشعارات عند توفر إصدارات جديدة
- ملاحظات الإصدار مع كل تحديث

---

**شكراً لاستخدام Q2 Web Scraper!**

نأمل أن تساعدك هذه الإضافة في كشط البيانات بكفاءة وسهولة. لا تتردد في التواصل معنا لأي استفسارات أو اقتراحات.
