<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء أيقونات Q2</title>
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .icon-item {
            text-align: center;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .icon-item h3 {
            margin: 10px 0;
            color: #555;
        }
        
        svg {
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .download-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-right: 4px solid #2196f3;
        }
        
        .instructions h3 {
            color: #1976d2;
            margin-top: 0;
        }
        
        .code-block {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 إنشاء أيقونات إضافة Q2 Web Scraper</h1>
        
        <div class="instructions">
            <h3>📋 تعليمات الاستخدام:</h3>
            <ol>
                <li>انقر بالزر الأيمن على كل أيقونة أدناه</li>
                <li>اختر "حفظ الصورة باسم" (Save image as)</li>
                <li>احفظ الأيقونة في مجلد <code>icons/</code> بالاسم المحدد</li>
                <li>كرر العملية لجميع الأحجام</li>
            </ol>
        </div>
        
        <div class="icon-grid">
            <!-- أيقونة 16x16 -->
            <div class="icon-item">
                <h3>أيقونة 16×16</h3>
                <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg" id="icon16">
                    <defs>
                        <linearGradient id="grad16" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="16" height="16" fill="url(#grad16)" rx="2"/>
                    <text x="8" y="12" font-family="Arial, sans-serif" font-size="10" 
                          fill="white" text-anchor="middle" font-weight="bold">Q2</text>
                </svg>
                <br>
                <button class="download-btn" onclick="downloadSVG('icon16', 'icon16.png')">
                    تحميل icon16.png
                </button>
            </div>
            
            <!-- أيقونة 32x32 -->
            <div class="icon-item">
                <h3>أيقونة 32×32</h3>
                <svg width="32" height="32" xmlns="http://www.w3.org/2000/svg" id="icon32">
                    <defs>
                        <linearGradient id="grad32" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="32" height="32" fill="url(#grad32)" rx="4"/>
                    <text x="16" y="22" font-family="Arial, sans-serif" font-size="16" 
                          fill="white" text-anchor="middle" font-weight="bold">Q2</text>
                </svg>
                <br>
                <button class="download-btn" onclick="downloadSVG('icon32', 'icon32.png')">
                    تحميل icon32.png
                </button>
            </div>
            
            <!-- أيقونة 48x48 -->
            <div class="icon-item">
                <h3>أيقونة 48×48</h3>
                <svg width="48" height="48" xmlns="http://www.w3.org/2000/svg" id="icon48">
                    <defs>
                        <linearGradient id="grad48" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="48" height="48" fill="url(#grad48)" rx="6"/>
                    <text x="24" y="32" font-family="Arial, sans-serif" font-size="24" 
                          fill="white" text-anchor="middle" font-weight="bold">Q2</text>
                    <circle cx="38" cy="10" r="3" fill="rgba(255,255,255,0.3)"/>
                </svg>
                <br>
                <button class="download-btn" onclick="downloadSVG('icon48', 'icon48.png')">
                    تحميل icon48.png
                </button>
            </div>
            
            <!-- أيقونة 128x128 -->
            <div class="icon-item">
                <h3>أيقونة 128×128</h3>
                <svg width="128" height="128" xmlns="http://www.w3.org/2000/svg" id="icon128">
                    <defs>
                        <linearGradient id="grad128" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                        </linearGradient>
                        <filter id="shadow">
                            <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
                        </filter>
                    </defs>
                    <rect width="128" height="128" fill="url(#grad128)" rx="16"/>
                    <text x="64" y="85" font-family="Arial, sans-serif" font-size="48" 
                          fill="white" text-anchor="middle" font-weight="bold" filter="url(#shadow)">Q2</text>
                    <circle cx="100" cy="28" r="8" fill="rgba(255,255,255,0.3)"/>
                    <rect x="20" y="100" width="88" height="4" fill="rgba(255,255,255,0.2)" rx="2"/>
                </svg>
                <br>
                <button class="download-btn" onclick="downloadSVG('icon128', 'icon128.png')">
                    تحميل icon128.png
                </button>
            </div>
        </div>
        
        <div class="instructions">
            <h3>🔧 طريقة بديلة - إنشاء الأيقونات برمجياً:</h3>
            <p>يمكنك استخدام الكود التالي لإنشاء الأيقونات تلقائياً:</p>
            <div class="code-block">
// JavaScript لتحويل SVG إلى PNG
function downloadSVG(svgId, filename) {
    const svg = document.getElementById(svgId);
    const svgData = new XMLSerializer().serializeToString(svg);
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    canvas.width = svg.width.baseVal.value;
    canvas.height = svg.height.baseVal.value;
    
    img.onload = function() {
        ctx.drawImage(img, 0, 0);
        const pngFile = canvas.toDataURL('image/png');
        const downloadLink = document.createElement('a');
        downloadLink.download = filename;
        downloadLink.href = pngFile;
        downloadLink.click();
    };
    
    img.src = 'data:image/svg+xml;base64,' + btoa(svgData);
}
            </div>
        </div>
        
        <div class="instructions">
            <h3>📁 هيكل الملفات المطلوب:</h3>
            <div class="code-block">
icons/
├── icon16.png   (16×16 بكسل)
├── icon32.png   (32×32 بكسل)  
├── icon48.png   (48×48 بكسل)
└── icon128.png  (128×128 بكسل)
            </div>
        </div>
        
        <div class="instructions">
            <h3>🎨 تخصيص الأيقونات:</h3>
            <p>يمكنك تعديل الألوان والتصميم عبر تغيير قيم SVG:</p>
            <ul>
                <li><strong>الألوان:</strong> غير قيم <code>stop-color</code> في <code>linearGradient</code></li>
                <li><strong>النص:</strong> غير محتوى عنصر <code>text</code></li>
                <li><strong>الحجم:</strong> غير قيم <code>width</code> و <code>height</code></li>
                <li><strong>الزوايا:</strong> غير قيمة <code>rx</code> في <code>rect</code></li>
            </ul>
        </div>
    </div>

    <script>
        // دالة تحويل SVG إلى PNG وتحميله
        function downloadSVG(svgId, filename) {
            const svg = document.getElementById(svgId);
            const svgData = new XMLSerializer().serializeToString(svg);
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            // تعيين أبعاد الكانفاس
            canvas.width = svg.width.baseVal.value;
            canvas.height = svg.height.baseVal.value;
            
            img.onload = function() {
                // رسم الصورة على الكانفاس
                ctx.drawImage(img, 0, 0);
                
                // تحويل إلى PNG وتحميل
                const pngFile = canvas.toDataURL('image/png');
                const downloadLink = document.createElement('a');
                downloadLink.download = filename;
                downloadLink.href = pngFile;
                downloadLink.click();
            };
            
            // تحويل SVG إلى Data URL
            img.src = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgData)));
        }
        
        // إضافة تأثيرات تفاعلية
        document.querySelectorAll('.download-btn').forEach(btn => {
            btn.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.3)';
            });
            
            btn.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            });
        });
        
        // رسالة ترحيب
        console.log('🎨 مرحباً بك في أداة إنشاء أيقونات Q2 Web Scraper!');
        console.log('💡 نصيحة: يمكنك تعديل ألوان الأيقونات عبر تغيير قيم linearGradient في كود SVG');
    </script>
</body>
</html>
