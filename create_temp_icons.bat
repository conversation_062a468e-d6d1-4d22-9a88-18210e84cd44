@echo off
echo Creating temporary icons for Q2 Web Scraper...
echo.

REM Create icons directory if it doesn't exist
if not exist "icons" mkdir icons

REM Create a simple PowerShell script to generate basic PNG icons
echo Creating PowerShell script...

(
echo Add-Type -AssemblyName System.Drawing
echo.
echo # Function to create a simple icon
echo function Create-Icon {
echo     param^([int]$Size, [string]$Filename^)
echo.
echo     # Create bitmap
echo     $bitmap = New-Object System.Drawing.Bitmap^($Size, $Size^)
echo     $graphics = [System.Drawing.Graphics]::FromImage^($bitmap^)
echo.
echo     # Set high quality rendering
echo     $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
echo     $graphics.TextRenderingHint = [System.Drawing.Text.TextRenderingHint]::AntiAlias
echo.
echo     # Create gradient brush
echo     $rect = New-Object System.Drawing.Rectangle^(0, 0, $Size, $Size^)
echo     $color1 = [System.Drawing.Color]::FromArgb^(102, 126, 234^)  # 667eea
echo     $color2 = [System.Drawing.Color]::FromArgb^(118, 75, 162^)   # 764ba2
echo     $brush = New-Object System.Drawing.Drawing2D.LinearGradientBrush^($rect, $color1, $color2, 45^)
echo.
echo     # Fill background
echo     $graphics.FillRectangle^($brush, $rect^)
echo.
echo     # Draw text
echo     $fontSize = [Math]::Max^(8, [Math]::Floor^($Size / 3^)^)
echo     $font = New-Object System.Drawing.Font^("Arial", $fontSize, [System.Drawing.FontStyle]::Bold^)
echo     $textBrush = New-Object System.Drawing.SolidBrush^([System.Drawing.Color]::White^)
echo     $format = New-Object System.Drawing.StringFormat
echo     $format.Alignment = [System.Drawing.StringAlignment]::Center
echo     $format.LineAlignment = [System.Drawing.StringAlignment]::Center
echo.
echo     $graphics.DrawString^("Q2", $font, $textBrush, $rect, $format^)
echo.
echo     # Save image
echo     $bitmap.Save^($Filename, [System.Drawing.Imaging.ImageFormat]::Png^)
echo.
echo     # Cleanup
echo     $graphics.Dispose^(^)
echo     $bitmap.Dispose^(^)
echo     $brush.Dispose^(^)
echo     $font.Dispose^(^)
echo     $textBrush.Dispose^(^)
echo.
echo     Write-Host "Created $Filename ^($Size x $Size^)"
echo }
echo.
echo # Create all required icons
echo Write-Host "Creating Q2 Web Scraper icons..."
echo Write-Host ""
echo.
echo Create-Icon -Size 16 -Filename "icons\icon16.png"
echo Create-Icon -Size 32 -Filename "icons\icon32.png"
echo Create-Icon -Size 48 -Filename "icons\icon48.png"
echo Create-Icon -Size 128 -Filename "icons\icon128.png"
echo.
echo Write-Host ""
echo Write-Host "All icons created successfully!" -ForegroundColor Green
echo Write-Host "Files created in icons\ directory:" -ForegroundColor Yellow
echo Write-Host "  - icon16.png"
echo Write-Host "  - icon32.png"
echo Write-Host "  - icon48.png"
echo Write-Host "  - icon128.png"
echo Write-Host ""
echo Write-Host "You can now install the Chrome extension!" -ForegroundColor Cyan
) > create_icons.ps1

echo Running PowerShell script to create icons...
echo.

powershell -ExecutionPolicy Bypass -File create_icons.ps1

echo.
echo Cleaning up temporary files...
del create_icons.ps1

echo.
echo Done! Check the icons directory for the created PNG files.
echo You can now install the Chrome extension.
echo.
pause
