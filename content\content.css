/**
 * ملف CSS للـ Content Script
 * يحتوي على الأنماط المطلوبة للتمييز البصري والعناصر المحقونة
 */

/* أنماط التمييز البصري للعناصر المكتشفة */

/* تمييز الجداول */
.q2-table-highlight {
    outline: 2px solid #4CAF50 !important;
    background-color: rgba(76, 175, 80, 0.1) !important;
    border-radius: 4px !important;
    position: relative !important;
    animation: q2-pulse-green 2s infinite !important;
}

/* تمييز الجدول المحدد */
.q2-selected-table {
    outline: 3px solid #2196F3 !important;
    background-color: rgba(33, 150, 243, 0.15) !important;
    border-radius: 4px !important;
    position: relative !important;
    box-shadow: 0 0 15px rgba(33, 150, 243, 0.3) !important;
}

/* تمييز المنتجات */
.q2-product-highlight {
    outline: 2px solid #FF9800 !important;
    background-color: rgba(255, 152, 0, 0.1) !important;
    border-radius: 4px !important;
    position: relative !important;
    animation: q2-pulse-orange 2s infinite !important;
}

/* تمييز الصور */
.q2-image-highlight {
    outline: 2px solid #E91E63 !important;
    box-shadow: 0 0 10px rgba(233, 30, 99, 0.4) !important;
    border-radius: 4px !important;
    position: relative !important;
    animation: q2-pulse-pink 2s infinite !important;
}

/* شارة Q2 للعناصر المميزة */
.q2-highlight-badge::after {
    content: 'Q2';
    position: absolute !important;
    top: -15px !important;
    right: -10px !important;
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    padding: 3px 8px !important;
    font-size: 10px !important;
    font-weight: bold !important;
    border-radius: 12px !important;
    font-family: 'Segoe UI', Arial, sans-serif !important;
    z-index: 10000 !important;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2) !important;
    border: 1px solid rgba(255,255,255,0.3) !important;
}

/* أنماط الرسوم المتحركة */
@keyframes q2-pulse-green {
    0% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(76, 175, 80, 0); }
    100% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0); }
}

@keyframes q2-pulse-orange {
    0% { box-shadow: 0 0 0 0 rgba(255, 152, 0, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(255, 152, 0, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 152, 0, 0); }
}

@keyframes q2-pulse-pink {
    0% { box-shadow: 0 0 0 0 rgba(233, 30, 99, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(233, 30, 99, 0); }
    100% { box-shadow: 0 0 0 0 rgba(233, 30, 99, 0); }
}

@keyframes q2-pulse-blue {
    0% { box-shadow: 0 0 0 0 rgba(33, 150, 243, 0.7); }
    70% { box-shadow: 0 0 0 15px rgba(33, 150, 243, 0); }
    100% { box-shadow: 0 0 0 0 rgba(33, 150, 243, 0); }
}

/* أنماط للعناصر التفاعلية المحقونة */

/* نافذة معاينة البيانات */
.q2-preview-modal {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    background: white !important;
    border-radius: 8px !important;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3) !important;
    z-index: 10001 !important;
    max-width: 80vw !important;
    max-height: 80vh !important;
    overflow: auto !important;
    padding: 20px !important;
    direction: rtl !important;
    font-family: 'Segoe UI', Arial, sans-serif !important;
}

/* خلفية النافذة المنبثقة */
.q2-modal-backdrop {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0,0,0,0.5) !important;
    z-index: 10000 !important;
}

/* أزرار التحكم العائمة */
.q2-floating-controls {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    border-radius: 8px !important;
    padding: 10px !important;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2) !important;
    z-index: 9999 !important;
    display: flex !important;
    gap: 10px !important;
    direction: rtl !important;
}

.q2-floating-btn {
    background: rgba(255,255,255,0.2) !important;
    border: 1px solid rgba(255,255,255,0.3) !important;
    color: white !important;
    padding: 8px 12px !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    font-size: 12px !important;
    font-family: 'Segoe UI', Arial, sans-serif !important;
    transition: all 0.3s ease !important;
}

.q2-floating-btn:hover {
    background: rgba(255,255,255,0.3) !important;
    transform: translateY(-1px) !important;
}

/* مؤشر التقدم */
.q2-progress-indicator {
    position: fixed !important;
    top: 10px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    padding: 10px 20px !important;
    border-radius: 20px !important;
    font-size: 14px !important;
    font-family: 'Segoe UI', Arial, sans-serif !important;
    z-index: 10002 !important;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2) !important;
    direction: rtl !important;
}

/* شريط التقدم */
.q2-progress-bar {
    width: 100% !important;
    height: 4px !important;
    background: rgba(255,255,255,0.3) !important;
    border-radius: 2px !important;
    margin-top: 8px !important;
    overflow: hidden !important;
}

.q2-progress-fill {
    height: 100% !important;
    background: white !important;
    border-radius: 2px !important;
    transition: width 0.3s ease !important;
    width: 0% !important;
}

/* تلميحات الأدوات */
.q2-tooltip {
    position: absolute !important;
    background: rgba(0,0,0,0.8) !important;
    color: white !important;
    padding: 5px 10px !important;
    border-radius: 4px !important;
    font-size: 12px !important;
    font-family: 'Segoe UI', Arial, sans-serif !important;
    z-index: 10003 !important;
    pointer-events: none !important;
    white-space: nowrap !important;
    direction: rtl !important;
}

.q2-tooltip::after {
    content: '' !important;
    position: absolute !important;
    top: 100% !important;
    left: 50% !important;
    margin-left: -5px !important;
    border-width: 5px !important;
    border-style: solid !important;
    border-color: rgba(0,0,0,0.8) transparent transparent transparent !important;
}

/* أنماط للجداول المحسنة */
.q2-enhanced-table {
    border-collapse: collapse !important;
    width: 100% !important;
    margin: 10px 0 !important;
}

.q2-enhanced-table th,
.q2-enhanced-table td {
    border: 1px solid #ddd !important;
    padding: 8px !important;
    text-align: right !important;
}

.q2-enhanced-table th {
    background-color: #f2f2f2 !important;
    font-weight: bold !important;
}

.q2-enhanced-table tr:nth-child(even) {
    background-color: #f9f9f9 !important;
}

.q2-enhanced-table tr:hover {
    background-color: #f5f5f5 !important;
}

/* أنماط للعناصر المحددة */
.q2-selected-element {
    outline: 3px solid #2196F3 !important;
    background-color: rgba(33, 150, 243, 0.1) !important;
    position: relative !important;
}

.q2-selected-element::before {
    content: '✓ محدد' !important;
    position: absolute !important;
    top: -25px !important;
    right: 0 !important;
    background: #2196F3 !important;
    color: white !important;
    padding: 2px 8px !important;
    font-size: 10px !important;
    border-radius: 3px !important;
    font-family: 'Segoe UI', Arial, sans-serif !important;
    z-index: 10000 !important;
}

/* أنماط للعناصر المستبعدة */
.q2-excluded-element {
    opacity: 0.5 !important;
    filter: grayscale(50%) !important;
    position: relative !important;
}

.q2-excluded-element::before {
    content: '✗ مستبعد' !important;
    position: absolute !important;
    top: -25px !important;
    right: 0 !important;
    background: #f44336 !important;
    color: white !important;
    padding: 2px 8px !important;
    font-size: 10px !important;
    border-radius: 3px !important;
    font-family: 'Segoe UI', Arial, sans-serif !important;
    z-index: 10000 !important;
}

/* أنماط للشاشات الصغيرة */
@media (max-width: 768px) {
    .q2-floating-controls {
        top: 10px !important;
        right: 10px !important;
        padding: 8px !important;
    }
    
    .q2-floating-btn {
        padding: 6px 10px !important;
        font-size: 11px !important;
    }
    
    .q2-preview-modal {
        max-width: 95vw !important;
        max-height: 90vh !important;
        padding: 15px !important;
    }
    
    .q2-progress-indicator {
        top: 5px !important;
        padding: 8px 15px !important;
        font-size: 12px !important;
    }
}

/* إخفاء العناصر المؤقتة */
.q2-hidden {
    display: none !important;
}

/* أنماط للرسوم المتحركة المتقدمة */
.q2-fade-in {
    animation: q2-fadeIn 0.3s ease-in !important;
}

.q2-fade-out {
    animation: q2-fadeOut 0.3s ease-out !important;
}

.q2-slide-in {
    animation: q2-slideIn 0.3s ease-out !important;
}

@keyframes q2-fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes q2-fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes q2-slideIn {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* أنماط للطباعة */
@media print {
    .q2-floating-controls,
    .q2-progress-indicator,
    .q2-tooltip,
    .q2-modal-backdrop {
        display: none !important;
    }
    
    .q2-table-highlight,
    .q2-product-highlight,
    .q2-image-highlight,
    .q2-selected-table {
        outline: none !important;
        background: none !important;
        box-shadow: none !important;
        animation: none !important;
    }
}
