/**
 * Content Script لإضافة Q2 Web Scraper
 * يعمل داخل صفحات الويب لكشط البيانات والتفاعل مع العناصر
 */

// متغيرات عامة
let isScrapingActive = false;
let highlightedElements = [];
let scrapingMode = null;

// تهيئة Content Script
(function() {
    console.log('Q2 Web Scraper Content Script تم تحميله');
    initializeContentScript();
})();

/**
 * تهيئة Content Script وإعداد مستمعي الأحداث
 */
function initializeContentScript() {
    // إضافة مستمع للرسائل من popup
    chrome.runtime.onMessage.addListener(handleMessage);
    
    // إضافة الأنماط المطلوبة للتمييز البصري
    injectStyles();
    
    console.log('تم تهيئة Content Script بنجاح');
}

/**
 * معالج الرسائل الواردة من popup أو background script
 * @param {Object} request - الرسالة الواردة
 * @param {Object} sender - مرسل الرسالة
 * @param {Function} sendResponse - دالة الرد
 */
function handleMessage(request, sender, sendResponse) {
    console.log('تم استلام رسالة:', request);
    
    switch (request.action) {
        case 'ping':
            sendResponse({ success: true, message: 'Content script جاهز' });
            break;
            
        case 'scanTables':
            scanTables().then(result => sendResponse(result));
            return true; // للإشارة إلى أن الرد سيكون غير متزامن
            
        case 'getTableColumns':
            getTableColumns(request.tableIndex).then(result => sendResponse(result));
            return true;
            
        case 'extractTableData':
            extractTableData(request.tableIndex, request.selectedColumns).then(result => sendResponse(result));
            return true;
            
        case 'scanProducts':
            scanProducts(request.options).then(result => sendResponse(result));
            return true;
            
        case 'scanImages':
            scanImages(request.minSize, request.formats).then(result => sendResponse(result));
            return true;
            
        default:
            sendResponse({ success: false, message: 'إجراء غير معروف' });
    }
}

/**
 * فحص وتحليل الجداول الموجودة في الصفحة
 * @returns {Promise<Object>} نتيجة الفحص
 */
async function scanTables() {
    try {
        const tables = document.querySelectorAll('table');
        const tableData = [];
        
        tables.forEach((table, index) => {
            const rows = table.querySelectorAll('tr');
            const columns = rows.length > 0 ? rows[0].querySelectorAll('th, td').length : 0;
            
            // تجاهل الجداول الصغيرة جداً أو الفارغة
            if (rows.length > 1 && columns > 0) {
                tableData.push({
                    index: index,
                    rows: rows.length,
                    columns: columns,
                    element: table,
                    hasHeaders: table.querySelector('thead') !== null || 
                              (rows[0] && rows[0].querySelectorAll('th').length > 0)
                });
                
                // إضافة تمييز بصري للجدول
                highlightElement(table, 'table-highlight');
            }
        });
        
        console.log(`تم العثور على ${tableData.length} جدول`);
        
        return {
            success: true,
            tables: tableData.map(t => ({
                index: t.index,
                rows: t.rows,
                columns: t.columns,
                hasHeaders: t.hasHeaders
            }))
        };
    } catch (error) {
        console.error('خطأ في فحص الجداول:', error);
        return { success: false, error: error.message };
    }
}

/**
 * الحصول على أعمدة جدول محدد
 * @param {number} tableIndex - فهرس الجدول
 * @returns {Promise<Object>} أعمدة الجدول
 */
async function getTableColumns(tableIndex) {
    try {
        const tables = document.querySelectorAll('table');
        
        if (tableIndex >= tables.length) {
            throw new Error('فهرس الجدول غير صحيح');
        }
        
        const table = tables[tableIndex];
        const firstRow = table.querySelector('tr');
        
        if (!firstRow) {
            throw new Error('الجدول فارغ');
        }
        
        const columns = [];
        const cells = firstRow.querySelectorAll('th, td');
        
        cells.forEach((cell, index) => {
            const text = cell.textContent.trim();
            columns.push(text || `العمود ${index + 1}`);
        });
        
        // تمييز الجدول المحدد
        clearHighlights();
        highlightElement(table, 'selected-table');
        
        return {
            success: true,
            columns: columns
        };
    } catch (error) {
        console.error('خطأ في الحصول على أعمدة الجدول:', error);
        return { success: false, error: error.message };
    }
}

/**
 * استخراج البيانات من جدول محدد
 * @param {number} tableIndex - فهرس الجدول
 * @param {Array} selectedColumns - الأعمدة المحددة
 * @returns {Promise<Object>} البيانات المستخرجة
 */
async function extractTableData(tableIndex, selectedColumns) {
    try {
        const tables = document.querySelectorAll('table');
        
        if (tableIndex >= tables.length) {
            throw new Error('فهرس الجدول غير صحيح');
        }
        
        const table = tables[tableIndex];
        const rows = table.querySelectorAll('tr');
        const data = [];
        
        // تحديد ما إذا كان الصف الأول يحتوي على رؤوس
        const hasHeaders = table.querySelector('thead') !== null || 
                          (rows[0] && rows[0].querySelectorAll('th').length > 0);
        
        const startRow = hasHeaders ? 1 : 0;
        const headers = [];
        
        // الحصول على الرؤوس
        if (hasHeaders && rows[0]) {
            const headerCells = rows[0].querySelectorAll('th, td');
            selectedColumns.forEach(colIndex => {
                if (colIndex < headerCells.length) {
                    headers.push(headerCells[colIndex].textContent.trim() || `العمود ${colIndex + 1}`);
                }
            });
        } else {
            selectedColumns.forEach(colIndex => {
                headers.push(`العمود ${colIndex + 1}`);
            });
        }
        
        // استخراج البيانات
        for (let i = startRow; i < rows.length; i++) {
            const row = rows[i];
            const cells = row.querySelectorAll('td, th');
            const rowData = {};
            
            selectedColumns.forEach((colIndex, headerIndex) => {
                if (colIndex < cells.length) {
                    const cellContent = extractCellContent(cells[colIndex]);
                    rowData[headers[headerIndex]] = cellContent;
                }
            });
            
            // إضافة الصف فقط إذا كان يحتوي على بيانات
            if (Object.values(rowData).some(value => value && value.trim() !== '')) {
                data.push(rowData);
            }
        }
        
        console.log(`تم استخراج ${data.length} صف من البيانات`);
        
        return {
            success: true,
            data: data
        };
    } catch (error) {
        console.error('خطأ في استخراج بيانات الجدول:', error);
        return { success: false, error: error.message };
    }
}

/**
 * استخراج محتوى خلية الجدول
 * @param {Element} cell - خلية الجدول
 * @returns {string} المحتوى المستخرج
 */
function extractCellContent(cell) {
    // البحث عن الروابط
    const link = cell.querySelector('a');
    if (link) {
        return {
            text: link.textContent.trim(),
            url: link.href
        };
    }
    
    // البحث عن الصور
    const img = cell.querySelector('img');
    if (img) {
        return {
            text: img.alt || 'صورة',
            src: img.src
        };
    }
    
    // النص العادي
    return cell.textContent.trim();
}

/**
 * فحص المنتجات في الصفحة
 * @param {Array} options - خيارات البحث
 * @returns {Promise<Object>} المنتجات المكتشفة
 */
async function scanProducts(options) {
    try {
        const products = [];
        
        // محددات شائعة للمنتجات في مواقع التجارة الإلكترونية
        const productSelectors = [
            '.product', '.product-item', '.item', '.product-card',
            '[data-product]', '[data-item]', '.listing-item',
            '.search-result', '.product-tile', '.product-box'
        ];
        
        let productElements = [];
        
        // البحث عن عناصر المنتجات
        for (const selector of productSelectors) {
            const elements = document.querySelectorAll(selector);
            if (elements.length > 0) {
                productElements = Array.from(elements);
                break;
            }
        }
        
        // إذا لم نجد منتجات بالمحددات الشائعة، نبحث بطريقة أخرى
        if (productElements.length === 0) {
            productElements = findProductsByPattern();
        }
        
        // استخراج بيانات كل منتج
        productElements.forEach((element, index) => {
            const product = extractProductData(element, options);
            if (product && Object.keys(product).length > 0) {
                product.index = index;
                products.push(product);
                highlightElement(element, 'product-highlight');
            }
        });
        
        console.log(`تم العثور على ${products.length} منتج`);
        
        return {
            success: true,
            data: products
        };
    } catch (error) {
        console.error('خطأ في فحص المنتجات:', error);
        return { success: false, error: error.message };
    }
}

/**
 * البحث عن المنتجات بناءً على الأنماط الشائعة
 * @returns {Array} عناصر المنتجات المكتشفة
 */
function findProductsByPattern() {
    const products = [];
    
    // البحث عن عناصر تحتوي على سعر ونص
    const priceSelectors = [
        '[class*="price"]', '[class*="cost"]', '[class*="amount"]',
        '[data-price]', '.currency', '[class*="money"]'
    ];
    
    priceSelectors.forEach(selector => {
        const priceElements = document.querySelectorAll(selector);
        priceElements.forEach(priceEl => {
            // البحث عن العنصر الأب الذي قد يحتوي على معلومات المنتج
            let productContainer = priceEl.closest('[class*="product"], [class*="item"], [class*="card"]');
            if (!productContainer) {
                productContainer = priceEl.parentElement;
                // التحقق من أن العنصر الأب يحتوي على نص كافي
                while (productContainer && productContainer.textContent.trim().length < 20) {
                    productContainer = productContainer.parentElement;
                }
            }
            
            if (productContainer && !products.includes(productContainer)) {
                products.push(productContainer);
            }
        });
    });
    
    return products;
}

/**
 * استخراج بيانات منتج من عنصر HTML
 * @param {Element} element - عنصر المنتج
 * @param {Array} options - الخيارات المطلوبة
 * @returns {Object} بيانات المنتج
 */
function extractProductData(element, options) {
    const product = {};
    
    if (options.includes('name')) {
        product.name = extractProductName(element);
    }
    
    if (options.includes('price')) {
        product.price = extractProductPrice(element);
    }
    
    if (options.includes('image')) {
        product.image = extractProductImage(element);
    }
    
    if (options.includes('description')) {
        product.description = extractProductDescription(element);
    }
    
    if (options.includes('rating')) {
        product.rating = extractProductRating(element);
    }
    
    return product;
}

/**
 * استخراج اسم المنتج
 * @param {Element} element - عنصر المنتج
 * @returns {string} اسم المنتج
 */
function extractProductName(element) {
    const nameSelectors = [
        'h1', 'h2', 'h3', 'h4',
        '[class*="title"]', '[class*="name"]', '[class*="product-name"]',
        '.product-title', 'a[title]', '[data-title]'
    ];
    
    for (const selector of nameSelectors) {
        const nameEl = element.querySelector(selector);
        if (nameEl) {
            const text = nameEl.textContent.trim();
            if (text.length > 3 && text.length < 200) {
                return text;
            }
        }
    }
    
    // إذا لم نجد اسم محدد، نأخذ أول نص طويل نسبياً
    const textNodes = getTextNodes(element);
    for (const node of textNodes) {
        const text = node.textContent.trim();
        if (text.length > 10 && text.length < 150) {
            return text;
        }
    }
    
    return null;
}

/**
 * استخراج سعر المنتج
 * @param {Element} element - عنصر المنتج
 * @returns {string} سعر المنتج
 */
function extractProductPrice(element) {
    const priceSelectors = [
        '[class*="price"]', '[class*="cost"]', '[class*="amount"]',
        '[data-price]', '.currency', '[class*="money"]',
        '[class*="sale"]', '[class*="discount"]'
    ];
    
    for (const selector of priceSelectors) {
        const priceEl = element.querySelector(selector);
        if (priceEl) {
            const text = priceEl.textContent.trim();
            // البحث عن أرقام مع رموز العملة
            const priceMatch = text.match(/[\d,.]+(\.?\d{0,2})?\s*[₹$€£¥₩₪﷼]/g) || 
                             text.match(/[₹$€£¥₩₪﷼]\s*[\d,.]+/g) ||
                             text.match(/[\d,.]+(\.?\d{0,2})?/g);
            
            if (priceMatch) {
                return priceMatch[0];
            }
        }
    }
    
    return null;
}

/**
 * استخراج صورة المنتج
 * @param {Element} element - عنصر المنتج
 * @returns {string} رابط صورة المنتج
 */
function extractProductImage(element) {
    const img = element.querySelector('img');
    if (img && img.src) {
        return img.src;
    }
    
    // البحث عن صور الخلفية
    const elementsWithBg = element.querySelectorAll('[style*="background-image"]');
    for (const el of elementsWithBg) {
        const bgImage = el.style.backgroundImage;
        const urlMatch = bgImage.match(/url\(['"]?([^'"]+)['"]?\)/);
        if (urlMatch) {
            return urlMatch[1];
        }
    }
    
    return null;
}

/**
 * استخراج وصف المنتج
 * @param {Element} element - عنصر المنتج
 * @returns {string} وصف المنتج
 */
function extractProductDescription(element) {
    const descSelectors = [
        '[class*="description"]', '[class*="summary"]', '[class*="detail"]',
        'p', '.product-info', '[class*="spec"]'
    ];
    
    for (const selector of descSelectors) {
        const descEl = element.querySelector(selector);
        if (descEl) {
            const text = descEl.textContent.trim();
            if (text.length > 20 && text.length < 500) {
                return text;
            }
        }
    }
    
    return null;
}

/**
 * استخراج تقييم المنتج
 * @param {Element} element - عنصر المنتج
 * @returns {string} تقييم المنتج
 */
function extractProductRating(element) {
    const ratingSelectors = [
        '[class*="rating"]', '[class*="star"]', '[class*="review"]',
        '[data-rating]', '[class*="score"]'
    ];
    
    for (const selector of ratingSelectors) {
        const ratingEl = element.querySelector(selector);
        if (ratingEl) {
            const text = ratingEl.textContent.trim();
            // البحث عن تقييمات رقمية
            const ratingMatch = text.match(/(\d+\.?\d*)\s*\/?\s*(\d+)?|\d+\s*stars?/i);
            if (ratingMatch) {
                return ratingMatch[0];
            }
            
            // عد النجوم
            const stars = ratingEl.querySelectorAll('[class*="star"], [class*="★"]').length;
            if (stars > 0) {
                return `${stars} نجوم`;
            }
        }
    }
    
    return null;
}

/**
 * فحص الصور في الصفحة
 * @param {number} minSize - الحد الأدنى لحجم الصورة
 * @param {Array} formats - صيغ الصور المطلوبة
 * @returns {Promise<Object>} الصور المكتشفة
 */
async function scanImages(minSize, formats) {
    try {
        const images = [];
        const imgElements = document.querySelectorAll('img');
        
        for (const img of imgElements) {
            // التحقق من أن الصورة محملة ولها أبعاد
            if (img.complete && img.naturalWidth > 0 && img.naturalHeight > 0) {
                // التحقق من الحد الأدنى للحجم
                if (img.naturalWidth >= minSize && img.naturalHeight >= minSize) {
                    // التحقق من صيغة الصورة
                    const src = img.src;
                    const extension = getFileExtension(src);
                    
                    if (formats.length === 0 || formats.includes(extension)) {
                        const imageData = {
                            src: src,
                            alt: img.alt || '',
                            width: img.naturalWidth,
                            height: img.naturalHeight,
                            size: `${img.naturalWidth}x${img.naturalHeight}`,
                            format: extension,
                            title: img.title || ''
                        };
                        
                        images.push(imageData);
                        highlightElement(img, 'image-highlight');
                    }
                }
            }
        }
        
        // البحث عن صور الخلفية أيضاً
        const bgImages = findBackgroundImages(minSize, formats);
        images.push(...bgImages);
        
        console.log(`تم العثور على ${images.length} صورة`);
        
        return {
            success: true,
            data: images
        };
    } catch (error) {
        console.error('خطأ في فحص الصور:', error);
        return { success: false, error: error.message };
    }
}

/**
 * البحث عن صور الخلفية
 * @param {number} minSize - الحد الأدنى للحجم
 * @param {Array} formats - الصيغ المطلوبة
 * @returns {Array} صور الخلفية المكتشفة
 */
function findBackgroundImages(minSize, formats) {
    const bgImages = [];
    const elements = document.querySelectorAll('*');
    
    elements.forEach(element => {
        const style = window.getComputedStyle(element);
        const bgImage = style.backgroundImage;
        
        if (bgImage && bgImage !== 'none') {
            const urlMatch = bgImage.match(/url\(['"]?([^'"]+)['"]?\)/);
            if (urlMatch) {
                const url = urlMatch[1];
                const extension = getFileExtension(url);
                
                if (formats.length === 0 || formats.includes(extension)) {
                    // محاولة تحديد حجم العنصر كتقدير لحجم الصورة
                    const rect = element.getBoundingClientRect();
                    if (rect.width >= minSize && rect.height >= minSize) {
                        bgImages.push({
                            src: url,
                            alt: 'صورة خلفية',
                            width: Math.round(rect.width),
                            height: Math.round(rect.height),
                            size: `${Math.round(rect.width)}x${Math.round(rect.height)} (تقديري)`,
                            format: extension,
                            type: 'background'
                        });
                        
                        highlightElement(element, 'image-highlight');
                    }
                }
            }
        }
    });
    
    return bgImages;
}

// دوال مساعدة

/**
 * الحصول على امتداد الملف من الرابط
 * @param {string} url - رابط الملف
 * @returns {string} امتداد الملف
 */
function getFileExtension(url) {
    try {
        const pathname = new URL(url).pathname;
        const extension = pathname.split('.').pop().toLowerCase();
        return ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(extension) ? extension : 'unknown';
    } catch {
        return 'unknown';
    }
}

/**
 * الحصول على عقد النص من عنصر
 * @param {Element} element - العنصر
 * @returns {Array} مصفوفة عقد النص
 */
function getTextNodes(element) {
    const textNodes = [];
    const walker = document.createTreeWalker(
        element,
        NodeFilter.SHOW_TEXT,
        null,
        false
    );
    
    let node;
    while (node = walker.nextNode()) {
        if (node.textContent.trim().length > 0) {
            textNodes.push(node);
        }
    }
    
    return textNodes;
}

/**
 * تمييز عنصر بصرياً
 * @param {Element} element - العنصر المراد تمييزه
 * @param {string} className - اسم الفئة للتمييز
 */
function highlightElement(element, className) {
    element.classList.add(className);
    highlightedElements.push({ element, className });
    
    // إزالة التمييز تلقائياً بعد 10 ثوان
    setTimeout(() => {
        element.classList.remove(className);
    }, 10000);
}

/**
 * مسح جميع التمييزات
 */
function clearHighlights() {
    highlightedElements.forEach(({ element, className }) => {
        element.classList.remove(className);
    });
    highlightedElements = [];
}

/**
 * حقن الأنماط المطلوبة للتمييز البصري
 */
function injectStyles() {
    if (document.getElementById('q2-scraper-styles')) {
        return; // الأنماط محقونة مسبقاً
    }
    
    const style = document.createElement('style');
    style.id = 'q2-scraper-styles';
    style.textContent = `
        .table-highlight {
            outline: 2px solid #4CAF50 !important;
            background-color: rgba(76, 175, 80, 0.1) !important;
        }
        
        .selected-table {
            outline: 3px solid #2196F3 !important;
            background-color: rgba(33, 150, 243, 0.1) !important;
        }
        
        .product-highlight {
            outline: 2px solid #FF9800 !important;
            background-color: rgba(255, 152, 0, 0.1) !important;
        }
        
        .image-highlight {
            outline: 2px solid #E91E63 !important;
            box-shadow: 0 0 10px rgba(233, 30, 99, 0.3) !important;
        }
        
        .q2-highlight {
            position: relative;
        }
        
        .q2-highlight::after {
            content: 'Q2';
            position: absolute;
            top: -20px;
            right: -10px;
            background: #667eea;
            color: white;
            padding: 2px 6px;
            font-size: 10px;
            border-radius: 3px;
            font-family: Arial, sans-serif;
            z-index: 10000;
        }
    `;
    
    document.head.appendChild(style);
}
