# إضافة Q2 لكشط البيانات من الويب

## نظرة عامة
إضافة Chrome متخصصة في كشط البيانات من صفحات الويب بطريقة ذكية وسهلة الاستخدام.

## الوظائف الرئيسية
- 🔍 **كشط الجداول**: التعرف التلقائي على الجداول واختيار الأعمدة المطلوبة
- 🛒 **كشط المنتجات**: استخراج معلومات المنتجات من مواقع التجارة الإلكترونية
- 🖼️ **كشط الصور**: تحميل وحفظ الصور من المواقع
- 💾 **تصدير البيانات**: حفظ البيانات بصيغ مختلفة (CSV, JSON, Excel)

## هيكل المشروع

```
Q2-Extension/
├── manifest.json          # ملف التكوين الرئيسي للإضافة
├── popup/                 # واجهة المستخدم الرئيسية
│   ├── popup.html        # صفحة الواجهة الرئيسية
│   ├── popup.css         # تنسيقات الواجهة
│   └── popup.js          # منطق الواجهة الرئيسية
├── content/               # سكريبت التفاعل مع صفحات الويب
│   ├── content.js        # منطق كشط البيانات
│   └── content.css       # تنسيقات العناصر المحقونة
├── background/            # سكريبت الخلفية
│   └── background.js     # إدارة الإضافة والتواصل
├── icons/                 # أيقونات الإضافة
└── assets/               # ملفات إضافية (خطوط، صور، إلخ)
```

## شرح المكونات الرئيسية

### 1. manifest.json
ملف التكوين الذي يحدد:
- صلاحيات الإضافة
- الملفات المطلوبة
- نقاط الدخول للإضافة

### 2. Popup (النافذة المنبثقة)
- **popup.html**: الواجهة الرئيسية التي تظهر عند النقر على أيقونة الإضافة
- **popup.js**: يحتوي على منطق التحكم في الواجهة والتواصل مع content script
- **popup.css**: تنسيقات الواجهة لجعلها جذابة وسهلة الاستخدام

### 3. Content Script
- **content.js**: يعمل داخل صفحات الويب لكشط البيانات
- **content.css**: تنسيقات للعناصر التي تُحقن في الصفحة

### 4. Background Script
- **background.js**: يعمل في الخلفية لإدارة الإضافة والتواصل بين المكونات

## التقنيات المستخدمة
- **Manifest V3**: أحدث إصدار من Chrome Extensions
- **JavaScript ES6+**: للبرمجة الحديثة
- **CSS3**: للتنسيقات المتقدمة
- **Chrome APIs**: للتفاعل مع المتصفح

## الخطوات التالية
1. تطوير واجهة المستخدم
2. إنشاء منطق كشط البيانات
3. إضافة وظائف التصدير
4. اختبار الإضافة
5. إنشاء دليل الاستخدام المفصل
