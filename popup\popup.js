/**
 * ملف JavaScript الرئيسي لواجهة المستخدم
 * يحتوي على منطق التحكم في التبويبات والتفاعل مع content script
 */

// متغيرات عامة
let currentTab = 'tables';
let scrapedData = null;
let currentTableIndex = -1;

// تهيئة الإضافة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializePopup();
    setupEventListeners();
    loadSettings();
    checkPageStatus();
});

/**
 * تهيئة الواجهة الرئيسية
 */
function initializePopup() {
    console.log('تم تهيئة واجهة Q2 Web Scraper');
    
    // إخفاء النتائج والتحميل في البداية
    hideElement('results');
    hideElement('loading');
    
    // تعيين التبويب النشط
    showTab('tables');
}

/**
 * إعداد مستمعي الأحداث لجميع العناصر التفاعلية
 */
function setupEventListeners() {
    // مستمعي أحداث التبويبات
    document.querySelectorAll('.tab-button').forEach(button => {
        button.addEventListener('click', function() {
            const tabName = this.getAttribute('data-tab');
            showTab(tabName);
        });
    });

    // مستمعي أحداث تبويب الجداول
    document.getElementById('scanTables').addEventListener('click', scanTablesInPage);
    document.getElementById('extractTableData').addEventListener('click', extractSelectedTableData);

    // مستمعي أحداث تبويب المنتجات
    document.getElementById('scanProducts').addEventListener('click', scanProductsInPage);

    // مستمعي أحداث تبويب الصور
    document.getElementById('scanImages').addEventListener('click', scanImagesInPage);

    // مستمعي أحداث الإعدادات
    document.getElementById('resetSettings').addEventListener('click', resetSettings);
    document.getElementById('exportFormat').addEventListener('change', saveSettings);
    document.getElementById('maxResults').addEventListener('change', saveSettings);
    document.getElementById('autoDownload').addEventListener('change', saveSettings);

    // مستمعي أحداث النتائج
    document.getElementById('previewData').addEventListener('click', previewData);
    document.getElementById('downloadData').addEventListener('click', downloadData);
    document.getElementById('clearResults').addEventListener('click', clearResults);
}

/**
 * عرض تبويب محدد وإخفاء الباقي
 * @param {string} tabName - اسم التبويب المراد عرضه
 */
function showTab(tabName) {
    // إزالة الفئة النشطة من جميع التبويبات
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active');
    });
    
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });

    // إضافة الفئة النشطة للتبويب المحدد
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    document.getElementById(tabName).classList.add('active');
    
    currentTab = tabName;
}

/**
 * فحص الجداول الموجودة في الصفحة الحالية
 */
async function scanTablesInPage() {
    showLoading('جاري فحص الجداول في الصفحة...');
    
    try {
        // الحصول على التبويب النشط
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        
        // إرسال رسالة لـ content script لفحص الجداول
        const response = await chrome.tabs.sendMessage(tab.id, {
            action: 'scanTables'
        });
        
        if (response && response.success) {
            displayFoundTables(response.tables);
            updateStatus('تم العثور على ' + response.tables.length + ' جدول');
        } else {
            showError('لم يتم العثور على جداول في هذه الصفحة');
        }
    } catch (error) {
        console.error('خطأ في فحص الجداول:', error);
        showError('حدث خطأ أثناء فحص الجداول');
    } finally {
        hideLoading();
    }
}

/**
 * عرض الجداول المكتشفة
 * @param {Array} tables - مصفوفة الجداول المكتشفة
 */
function displayFoundTables(tables) {
    const tablesContainer = document.getElementById('tablesContainer');
    const tablesList = document.getElementById('tablesList');
    
    tablesContainer.innerHTML = '';
    
    tables.forEach((table, index) => {
        const tableElement = document.createElement('div');
        tableElement.className = 'table-item';
        tableElement.innerHTML = `
            <div class="table-info">
                <strong>جدول ${index + 1}</strong>
                <span>${table.rows} صف × ${table.columns} عمود</span>
            </div>
            <button class="btn btn-small" onclick="selectTable(${index})">اختيار</button>
        `;
        tablesContainer.appendChild(tableElement);
    });
    
    showElement('tablesList');
}

/**
 * اختيار جدول محدد لكشط البيانات منه
 * @param {number} tableIndex - فهرس الجدول المحدد
 */
async function selectTable(tableIndex) {
    currentTableIndex = tableIndex;
    showLoading('جاري تحليل أعمدة الجدول...');
    
    try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        
        const response = await chrome.tabs.sendMessage(tab.id, {
            action: 'getTableColumns',
            tableIndex: tableIndex
        });
        
        if (response && response.success) {
            displayTableColumns(response.columns);
        } else {
            showError('فشل في تحليل أعمدة الجدول');
        }
    } catch (error) {
        console.error('خطأ في تحليل الجدول:', error);
        showError('حدث خطأ أثناء تحليل الجدول');
    } finally {
        hideLoading();
    }
}

/**
 * عرض أعمدة الجدول للاختيار
 * @param {Array} columns - مصفوفة أعمدة الجدول
 */
function displayTableColumns(columns) {
    const columnsContainer = document.getElementById('columnsContainer');
    const columnsSelection = document.getElementById('columnsSelection');
    
    columnsContainer.innerHTML = '';
    
    columns.forEach((column, index) => {
        const columnElement = document.createElement('label');
        columnElement.className = 'checkbox-label';
        columnElement.innerHTML = `
            <input type="checkbox" value="${index}" checked>
            <span class="checkmark"></span>
            ${column || `العمود ${index + 1}`}
        `;
        columnsContainer.appendChild(columnElement);
    });
    
    showElement('columnsSelection');
}

/**
 * استخراج البيانات من الأعمدة المحددة
 */
async function extractSelectedTableData() {
    const selectedColumns = Array.from(
        document.querySelectorAll('#columnsContainer input[type="checkbox"]:checked')
    ).map(checkbox => parseInt(checkbox.value));
    
    if (selectedColumns.length === 0) {
        showError('يرجى اختيار عمود واحد على الأقل');
        return;
    }
    
    showLoading('جاري استخراج البيانات...');
    
    try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        
        const response = await chrome.tabs.sendMessage(tab.id, {
            action: 'extractTableData',
            tableIndex: currentTableIndex,
            selectedColumns: selectedColumns
        });
        
        if (response && response.success) {
            scrapedData = response.data;
            displayResults(response.data, 'table');
            updateStatus(`تم استخراج ${response.data.length} صف من البيانات`);
        } else {
            showError('فشل في استخراج البيانات');
        }
    } catch (error) {
        console.error('خطأ في استخراج البيانات:', error);
        showError('حدث خطأ أثناء استخراج البيانات');
    } finally {
        hideLoading();
    }
}

/**
 * فحص المنتجات في الصفحة
 */
async function scanProductsInPage() {
    const selectedOptions = getSelectedProductOptions();
    
    if (selectedOptions.length === 0) {
        showError('يرجى اختيار خيار واحد على الأقل');
        return;
    }
    
    showLoading('جاري فحص المنتجات...');
    
    try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        
        const response = await chrome.tabs.sendMessage(tab.id, {
            action: 'scanProducts',
            options: selectedOptions
        });
        
        if (response && response.success) {
            scrapedData = response.data;
            displayResults(response.data, 'products');
            updateStatus(`تم العثور على ${response.data.length} منتج`);
        } else {
            showError('لم يتم العثور على منتجات في هذه الصفحة');
        }
    } catch (error) {
        console.error('خطأ في فحص المنتجات:', error);
        showError('حدث خطأ أثناء فحص المنتجات');
    } finally {
        hideLoading();
    }
}

/**
 * الحصول على خيارات المنتجات المحددة
 * @returns {Array} مصفوفة الخيارات المحددة
 */
function getSelectedProductOptions() {
    const options = [];
    
    if (document.getElementById('productName').checked) options.push('name');
    if (document.getElementById('productPrice').checked) options.push('price');
    if (document.getElementById('productImage').checked) options.push('image');
    if (document.getElementById('productDescription').checked) options.push('description');
    if (document.getElementById('productRating').checked) options.push('rating');
    
    return options;
}

/**
 * فحص الصور في الصفحة
 */
async function scanImagesInPage() {
    const minSize = parseInt(document.getElementById('minImageSize').value) || 100;
    const selectedFormats = getSelectedImageFormats();
    
    showLoading('جاري فحص الصور...');
    
    try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        
        const response = await chrome.tabs.sendMessage(tab.id, {
            action: 'scanImages',
            minSize: minSize,
            formats: selectedFormats
        });
        
        if (response && response.success) {
            scrapedData = response.data;
            displayResults(response.data, 'images');
            updateStatus(`تم العثور على ${response.data.length} صورة`);
        } else {
            showError('لم يتم العثور على صور مطابقة للمعايير');
        }
    } catch (error) {
        console.error('خطأ في فحص الصور:', error);
        showError('حدث خطأ أثناء فحص الصور');
    } finally {
        hideLoading();
    }
}

/**
 * الحصول على صيغ الصور المحددة
 * @returns {Array} مصفوفة صيغ الصور المحددة
 */
function getSelectedImageFormats() {
    return Array.from(
        document.querySelectorAll('.format-checkboxes input[type="checkbox"]:checked')
    ).map(checkbox => checkbox.value);
}

// دوال مساعدة للواجهة
function showElement(elementId) {
    document.getElementById(elementId).style.display = 'block';
}

function hideElement(elementId) {
    document.getElementById(elementId).style.display = 'none';
}

function showLoading(message = 'جاري المعالجة...') {
    document.querySelector('.loading span').textContent = message;
    showElement('loading');
}

function hideLoading() {
    hideElement('loading');
}

function updateStatus(message, type = 'success') {
    const statusText = document.querySelector('.status-text');
    const statusIndicator = document.querySelector('.status-indicator');
    
    statusText.textContent = message;
    
    // تغيير لون المؤشر حسب نوع الرسالة
    statusIndicator.style.background = type === 'error' ? '#f44336' : '#4CAF50';
}

function showError(message) {
    updateStatus(message, 'error');
    console.error(message);
}

/**
 * عرض النتائج
 * @param {Array} data - البيانات المستخرجة
 * @param {string} type - نوع البيانات
 */
function displayResults(data, type) {
    const resultsContent = document.getElementById('resultsContent');
    const resultsStats = document.getElementById('resultsStats');
    
    // عرض إحصائيات النتائج
    resultsStats.textContent = `تم استخراج ${data.length} عنصر من نوع ${type}`;
    
    // عرض عينة من البيانات
    if (data.length > 0) {
        const preview = data.slice(0, 3).map(item => 
            typeof item === 'object' ? JSON.stringify(item, null, 2) : item
        ).join('\n---\n');
        
        resultsContent.innerHTML = `<pre>${preview}</pre>`;
        if (data.length > 3) {
            resultsContent.innerHTML += `<p>... و ${data.length - 3} عنصر آخر</p>`;
        }
    }
    
    showElement('results');
}

// دوال الإعدادات
function loadSettings() {
    chrome.storage.sync.get(['exportFormat', 'maxResults', 'autoDownload'], function(result) {
        if (result.exportFormat) {
            document.getElementById('exportFormat').value = result.exportFormat;
        }
        if (result.maxResults) {
            document.getElementById('maxResults').value = result.maxResults;
        }
        if (result.autoDownload !== undefined) {
            document.getElementById('autoDownload').checked = result.autoDownload;
        }
    });
}

function saveSettings() {
    const settings = {
        exportFormat: document.getElementById('exportFormat').value,
        maxResults: parseInt(document.getElementById('maxResults').value),
        autoDownload: document.getElementById('autoDownload').checked
    };
    
    chrome.storage.sync.set(settings, function() {
        updateStatus('تم حفظ الإعدادات');
    });
}

function resetSettings() {
    document.getElementById('exportFormat').value = 'csv';
    document.getElementById('maxResults').value = '1000';
    document.getElementById('autoDownload').checked = false;
    
    saveSettings();
    updateStatus('تم إعادة تعيين الإعدادات');
}

// دوال النتائج
function previewData() {
    if (!scrapedData) {
        showError('لا توجد بيانات للمعاينة');
        return;
    }
    
    // فتح نافذة جديدة لمعاينة البيانات
    const previewWindow = window.open('', '_blank', 'width=800,height=600');
    previewWindow.document.write(`
        <html dir="rtl">
        <head>
            <title>معاينة البيانات - Q2</title>
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; direction: rtl; }
                pre { background: #f5f5f5; padding: 15px; border-radius: 5px; overflow: auto; }
                table { border-collapse: collapse; width: 100%; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                th { background: #f2f2f2; }
            </style>
        </head>
        <body>
            <h2>معاينة البيانات المستخرجة</h2>
            <pre>${JSON.stringify(scrapedData, null, 2)}</pre>
        </body>
        </html>
    `);
}

function downloadData() {
    if (!scrapedData) {
        showError('لا توجد بيانات للتحميل');
        return;
    }
    
    const format = document.getElementById('exportFormat').value;
    let content, filename, mimeType;
    
    switch (format) {
        case 'json':
            content = JSON.stringify(scrapedData, null, 2);
            filename = 'scraped_data.json';
            mimeType = 'application/json';
            break;
        case 'csv':
            content = convertToCSV(scrapedData);
            filename = 'scraped_data.csv';
            mimeType = 'text/csv';
            break;
        default:
            content = JSON.stringify(scrapedData, null, 2);
            filename = 'scraped_data.json';
            mimeType = 'application/json';
    }
    
    // تحميل الملف
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    
    chrome.downloads.download({
        url: url,
        filename: filename,
        saveAs: true
    }, function(downloadId) {
        if (downloadId) {
            updateStatus('تم بدء التحميل');
        } else {
            showError('فشل في تحميل الملف');
        }
    });
}

function convertToCSV(data) {
    if (!data || data.length === 0) return '';
    
    // الحصول على المفاتيح من العنصر الأول
    const headers = Object.keys(data[0]);
    
    // إنشاء صف الرؤوس
    let csv = headers.join(',') + '\n';
    
    // إضافة البيانات
    data.forEach(row => {
        const values = headers.map(header => {
            const value = row[header] || '';
            // تنظيف القيم وإضافة علامات اقتباس إذا لزم الأمر
            return typeof value === 'string' && value.includes(',') 
                ? `"${value.replace(/"/g, '""')}"` 
                : value;
        });
        csv += values.join(',') + '\n';
    });
    
    return csv;
}

function clearResults() {
    scrapedData = null;
    hideElement('results');
    updateStatus('تم مسح النتائج');
}

/**
 * فحص حالة الصفحة الحالية
 */
async function checkPageStatus() {
    try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        
        // التحقق من أن الصفحة تدعم كشط البيانات
        if (tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
            updateStatus('هذه الصفحة غير مدعومة', 'error');
            return;
        }
        
        // محاولة التواصل مع content script
        try {
            await chrome.tabs.sendMessage(tab.id, { action: 'ping' });
            updateStatus('جاهز للعمل');
        } catch (error) {
            // إذا لم يكن content script محمل، نحاول حقنه
            await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                files: ['content/content.js']
            });
            updateStatus('تم تحميل أدوات الكشط');
        }
    } catch (error) {
        console.error('خطأ في فحص حالة الصفحة:', error);
        updateStatus('خطأ في الاتصال', 'error');
    }
}
