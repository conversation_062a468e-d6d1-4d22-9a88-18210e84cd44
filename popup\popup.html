<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Q2 Web Scraper</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <!-- رأس الإضافة -->
    <header class="header">
        <div class="logo">
            <h1>Q2</h1>
            <span>كاشط البيانات</span>
        </div>
        <div class="status" id="connectionStatus">
            <span class="status-indicator"></span>
            <span class="status-text">جاهز</span>
        </div>
    </header>

    <!-- التبويبات الرئيسية -->
    <nav class="tabs">
        <button class="tab-button active" data-tab="tables">الجداول</button>
        <button class="tab-button" data-tab="products">المنتجات</button>
        <button class="tab-button" data-tab="images">الصور</button>
        <button class="tab-button" data-tab="settings">الإعدادات</button>
    </nav>

    <!-- محتوى التبويبات -->
    <main class="content">
        <!-- تبويب الجداول -->
        <div class="tab-content active" id="tables">
            <div class="section">
                <h3>كشط الجداول</h3>
                <button class="btn btn-primary" id="scanTables">
                    <span class="icon">🔍</span>
                    فحص الجداول في الصفحة
                </button>
                
                <div class="tables-list" id="tablesList" style="display: none;">
                    <h4>الجداول المكتشفة:</h4>
                    <div id="tablesContainer"></div>
                </div>
                
                <div class="columns-selection" id="columnsSelection" style="display: none;">
                    <h4>اختر الأعمدة المطلوبة:</h4>
                    <div id="columnsContainer"></div>
                    <button class="btn btn-success" id="extractTableData">
                        <span class="icon">📊</span>
                        استخراج البيانات
                    </button>
                </div>
            </div>
        </div>

        <!-- تبويب المنتجات -->
        <div class="tab-content" id="products">
            <div class="section">
                <h3>كشط المنتجات</h3>
                <div class="product-options">
                    <label class="checkbox-label">
                        <input type="checkbox" id="productName" checked>
                        <span class="checkmark"></span>
                        اسم المنتج
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" id="productPrice" checked>
                        <span class="checkmark"></span>
                        السعر
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" id="productImage">
                        <span class="checkmark"></span>
                        صورة المنتج
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" id="productDescription">
                        <span class="checkmark"></span>
                        الوصف
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" id="productRating">
                        <span class="checkmark"></span>
                        التقييم
                    </label>
                </div>
                <button class="btn btn-primary" id="scanProducts">
                    <span class="icon">🛒</span>
                    فحص المنتجات
                </button>
            </div>
        </div>

        <!-- تبويب الصور -->
        <div class="tab-content" id="images">
            <div class="section">
                <h3>كشط الصور</h3>
                <div class="image-options">
                    <div class="input-group">
                        <label for="minImageSize">الحد الأدنى لحجم الصورة (بكسل):</label>
                        <input type="number" id="minImageSize" value="100" min="50">
                    </div>
                    <div class="input-group">
                        <label for="imageFormats">صيغ الصور المطلوبة:</label>
                        <div class="format-checkboxes">
                            <label class="checkbox-label">
                                <input type="checkbox" value="jpg" checked>
                                <span class="checkmark"></span>
                                JPG
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" value="png" checked>
                                <span class="checkmark"></span>
                                PNG
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" value="gif">
                                <span class="checkmark"></span>
                                GIF
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" value="webp">
                                <span class="checkmark"></span>
                                WebP
                            </label>
                        </div>
                    </div>
                </div>
                <button class="btn btn-primary" id="scanImages">
                    <span class="icon">🖼️</span>
                    فحص الصور
                </button>
            </div>
        </div>

        <!-- تبويب الإعدادات -->
        <div class="tab-content" id="settings">
            <div class="section">
                <h3>الإعدادات</h3>
                <div class="setting-group">
                    <label for="exportFormat">صيغة التصدير الافتراضية:</label>
                    <select id="exportFormat">
                        <option value="csv">CSV</option>
                        <option value="json">JSON</option>
                        <option value="excel">Excel</option>
                    </select>
                </div>
                <div class="setting-group">
                    <label for="maxResults">الحد الأقصى للنتائج:</label>
                    <input type="number" id="maxResults" value="1000" min="10" max="10000">
                </div>
                <div class="setting-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="autoDownload">
                        <span class="checkmark"></span>
                        تحميل تلقائي للبيانات
                    </label>
                </div>
                <button class="btn btn-secondary" id="resetSettings">
                    <span class="icon">🔄</span>
                    إعادة تعيين الإعدادات
                </button>
            </div>
        </div>
    </main>

    <!-- منطقة النتائج -->
    <div class="results" id="results" style="display: none;">
        <div class="results-header">
            <h4>النتائج</h4>
            <div class="results-actions">
                <button class="btn btn-small" id="previewData">معاينة</button>
                <button class="btn btn-small btn-success" id="downloadData">تحميل</button>
                <button class="btn btn-small btn-danger" id="clearResults">مسح</button>
            </div>
        </div>
        <div class="results-content" id="resultsContent"></div>
        <div class="results-stats" id="resultsStats"></div>
    </div>

    <!-- تحميل البيانات -->
    <div class="loading" id="loading" style="display: none;">
        <div class="spinner"></div>
        <span>جاري معالجة البيانات...</span>
    </div>

    <script src="popup.js"></script>
</body>
</html>
