/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    width: 400px;
    min-height: 500px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    direction: rtl;
    text-align: right;
}

/* رأس الإضافة */
.header {
    background: rgba(255, 255, 255, 0.95);
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e0e0e0;
    backdrop-filter: blur(10px);
}

.logo h1 {
    font-size: 24px;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 2px;
}

.logo span {
    font-size: 12px;
    color: #666;
}

.status {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4CAF50;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.status-text {
    font-size: 12px;
    color: #666;
}

/* التبويبات */
.tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.9);
    border-bottom: 1px solid #e0e0e0;
}

.tab-button {
    flex: 1;
    padding: 12px 8px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 12px;
    color: #666;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.tab-button:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.tab-button.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

/* المحتوى */
.content {
    background: rgba(255, 255, 255, 0.95);
    min-height: 300px;
}

.tab-content {
    display: none;
    padding: 20px;
}

.tab-content.active {
    display: block;
}

.section h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 16px;
    border-bottom: 2px solid #667eea;
    padding-bottom: 5px;
}

/* الأزرار */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    margin: 5px 0;
    width: 100%;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.btn-secondary {
    background: #f5f5f5;
    color: #666;
    border: 1px solid #ddd;
}

.btn-secondary:hover {
    background: #e9e9e9;
}

.btn-danger {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    color: white;
}

.btn-small {
    padding: 6px 12px;
    font-size: 12px;
    width: auto;
}

/* خيارات المنتجات والصور */
.product-options, .image-options {
    margin: 15px 0;
}

.checkbox-label {
    display: flex;
    align-items: center;
    margin: 8px 0;
    cursor: pointer;
    font-size: 14px;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #ddd;
    border-radius: 3px;
    margin-left: 10px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #667eea;
    border-color: #667eea;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 2px;
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* مجموعات الإدخال */
.input-group {
    margin: 15px 0;
}

.input-group label {
    display: block;
    margin-bottom: 5px;
    font-size: 14px;
    color: #555;
}

.input-group input, .input-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    direction: ltr;
    text-align: left;
}

.format-checkboxes {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 8px;
}

.format-checkboxes .checkbox-label {
    margin: 0;
    flex: 0 0 auto;
}

/* قوائم الجداول */
.tables-list, .columns-selection {
    margin: 15px 0;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

.tables-list h4, .columns-selection h4 {
    color: #333;
    margin-bottom: 10px;
    font-size: 14px;
}

/* النتائج */
.results {
    background: rgba(255, 255, 255, 0.95);
    border-top: 1px solid #e0e0e0;
    max-height: 200px;
    overflow-y: auto;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    background: #f9f9f9;
}

.results-header h4 {
    color: #333;
    font-size: 14px;
}

.results-actions {
    display: flex;
    gap: 5px;
}

.results-content {
    padding: 15px 20px;
    font-size: 12px;
    max-height: 120px;
    overflow-y: auto;
}

.results-stats {
    padding: 10px 20px;
    background: #f0f0f0;
    font-size: 12px;
    color: #666;
    border-top: 1px solid #e0e0e0;
}

/* تحميل البيانات */
.loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 15px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 420px) {
    body {
        width: 100%;
    }
    
    .tab-button {
        font-size: 11px;
        padding: 10px 6px;
    }
    
    .content {
        padding: 15px;
    }
}
