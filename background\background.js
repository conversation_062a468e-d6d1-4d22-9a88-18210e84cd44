/**
 * Background Script لإضافة Q2 Web Scraper
 * يعمل في الخلفية لإدارة الإضافة والتواصل بين المكونات المختلفة
 */

// متغيرات عامة
let activeScrapingSessions = new Map();
let extensionSettings = {
    autoInject: true,
    maxConcurrentSessions: 5,
    defaultExportFormat: 'csv',
    enableNotifications: true
};

// تهيئة Background Script عند تثبيت الإضافة
chrome.runtime.onInstalled.addListener(function(details) {
    console.log('Q2 Web Scraper تم تثبيته بنجاح');
    
    if (details.reason === 'install') {
        // إعداد الإضافة للمرة الأولى
        initializeExtension();
    } else if (details.reason === 'update') {
        // تحديث الإضافة
        handleExtensionUpdate(details.previousVersion);
    }
});

/**
 * تهيئة الإضافة للمرة الأولى
 */
function initializeExtension() {
    // تعيين الإعدادات الافتراضية
    chrome.storage.sync.set(extensionSettings, function() {
        console.log('تم تعيين الإعدادات الافتراضية');
    });
    
    // إنشاء قوائم السياق (Context Menus)
    createContextMenus();
    
    // عرض رسالة ترحيب
    showWelcomeNotification();
}

/**
 * معالجة تحديث الإضافة
 * @param {string} previousVersion - الإصدار السابق
 */
function handleExtensionUpdate(previousVersion) {
    console.log(`تم تحديث الإضافة من الإصدار ${previousVersion}`);
    
    // تحديث الإعدادات إذا لزم الأمر
    updateSettingsIfNeeded(previousVersion);
    
    // عرض إشعار التحديث
    showUpdateNotification(previousVersion);
}

/**
 * إنشاء قوائم السياق
 */
function createContextMenus() {
    // قائمة سياق للجداول
    chrome.contextMenus.create({
        id: 'scrapeTable',
        title: 'كشط هذا الجدول - Q2',
        contexts: ['all'],
        documentUrlPatterns: ['http://*/*', 'https://*/*']
    });
    
    // قائمة سياق للصور
    chrome.contextMenus.create({
        id: 'scrapeImage',
        title: 'حفظ هذه الصورة - Q2',
        contexts: ['image'],
        documentUrlPatterns: ['http://*/*', 'https://*/*']
    });
    
    // قائمة سياق للصفحة كاملة
    chrome.contextMenus.create({
        id: 'scrapePage',
        title: 'كشط بيانات الصفحة - Q2',
        contexts: ['page'],
        documentUrlPatterns: ['http://*/*', 'https://*/*']
    });
}

/**
 * معالج النقر على قوائم السياق
 */
chrome.contextMenus.onClicked.addListener(function(info, tab) {
    console.log('تم النقر على قائمة السياق:', info.menuItemId);
    
    switch (info.menuItemId) {
        case 'scrapeTable':
            handleTableScraping(tab, info);
            break;
        case 'scrapeImage':
            handleImageScraping(tab, info);
            break;
        case 'scrapePage':
            handlePageScraping(tab, info);
            break;
    }
});

/**
 * معالجة كشط الجداول من قائمة السياق
 * @param {Object} tab - معلومات التبويب
 * @param {Object} info - معلومات النقر
 */
async function handleTableScraping(tab, info) {
    try {
        // حقن content script إذا لم يكن محقوناً
        await ensureContentScriptInjected(tab.id);
        
        // إرسال رسالة لبدء كشط الجداول
        const response = await chrome.tabs.sendMessage(tab.id, {
            action: 'scanTables',
            source: 'contextMenu'
        });
        
        if (response && response.success) {
            showNotification('تم العثور على ' + response.tables.length + ' جدول', 'success');
        } else {
            showNotification('لم يتم العثور على جداول في هذه الصفحة', 'warning');
        }
    } catch (error) {
        console.error('خطأ في كشط الجداول:', error);
        showNotification('حدث خطأ أثناء كشط الجداول', 'error');
    }
}

/**
 * معالجة كشط الصور من قائمة السياق
 * @param {Object} tab - معلومات التبويب
 * @param {Object} info - معلومات النقر
 */
async function handleImageScraping(tab, info) {
    try {
        if (info.srcUrl) {
            // تحميل الصورة مباشرة
            const downloadId = await chrome.downloads.download({
                url: info.srcUrl,
                filename: generateImageFilename(info.srcUrl),
                saveAs: false
            });
            
            if (downloadId) {
                showNotification('تم بدء تحميل الصورة', 'success');
            }
        }
    } catch (error) {
        console.error('خطأ في تحميل الصورة:', error);
        showNotification('فشل في تحميل الصورة', 'error');
    }
}

/**
 * معالجة كشط الصفحة كاملة
 * @param {Object} tab - معلومات التبويب
 * @param {Object} info - معلومات النقر
 */
async function handlePageScraping(tab, info) {
    try {
        // فتح popup الإضافة
        chrome.action.openPopup();
        
        showNotification('تم فتح أدوات كشط البيانات', 'info');
    } catch (error) {
        console.error('خطأ في فتح أدوات الكشط:', error);
    }
}

/**
 * التأكد من حقن content script
 * @param {number} tabId - معرف التبويب
 */
async function ensureContentScriptInjected(tabId) {
    try {
        // محاولة إرسال رسالة ping للتحقق من وجود content script
        await chrome.tabs.sendMessage(tabId, { action: 'ping' });
    } catch (error) {
        // إذا فشلت، نحقن content script
        await chrome.scripting.executeScript({
            target: { tabId: tabId },
            files: ['content/content.js']
        });
        
        // حقن CSS أيضاً
        await chrome.scripting.insertCSS({
            target: { tabId: tabId },
            files: ['content/content.css']
        });
        
        console.log('تم حقن content script في التبويب:', tabId);
    }
}

/**
 * معالج الرسائل من popup أو content scripts
 */
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    console.log('تم استلام رسالة في background:', request);
    
    switch (request.action) {
        case 'startScrapingSession':
            startScrapingSession(request.data, sender.tab).then(sendResponse);
            return true;
            
        case 'endScrapingSession':
            endScrapingSession(request.sessionId).then(sendResponse);
            return true;
            
        case 'saveScrapedData':
            saveScrapedData(request.data, request.format).then(sendResponse);
            return true;
            
        case 'getSettings':
            getExtensionSettings().then(sendResponse);
            return true;
            
        case 'updateSettings':
            updateExtensionSettings(request.settings).then(sendResponse);
            return true;
            
        case 'showNotification':
            showNotification(request.message, request.type);
            sendResponse({ success: true });
            break;
            
        default:
            sendResponse({ success: false, message: 'إجراء غير معروف' });
    }
});

/**
 * بدء جلسة كشط جديدة
 * @param {Object} data - بيانات الجلسة
 * @param {Object} tab - معلومات التبويب
 */
async function startScrapingSession(data, tab) {
    try {
        const sessionId = generateSessionId();
        const session = {
            id: sessionId,
            tabId: tab.id,
            url: tab.url,
            startTime: Date.now(),
            type: data.type,
            status: 'active',
            data: []
        };
        
        activeScrapingSessions.set(sessionId, session);
        
        console.log('تم بدء جلسة كشط جديدة:', sessionId);
        
        return { success: true, sessionId: sessionId };
    } catch (error) {
        console.error('خطأ في بدء جلسة الكشط:', error);
        return { success: false, error: error.message };
    }
}

/**
 * إنهاء جلسة كشط
 * @param {string} sessionId - معرف الجلسة
 */
async function endScrapingSession(sessionId) {
    try {
        const session = activeScrapingSessions.get(sessionId);
        
        if (session) {
            session.status = 'completed';
            session.endTime = Date.now();
            session.duration = session.endTime - session.startTime;
            
            // حفظ الجلسة في التخزين المحلي للمراجعة لاحقاً
            await saveSessionToStorage(session);
            
            // إزالة الجلسة من الجلسات النشطة
            activeScrapingSessions.delete(sessionId);
            
            console.log('تم إنهاء جلسة الكشط:', sessionId);
            
            return { success: true };
        } else {
            throw new Error('جلسة غير موجودة');
        }
    } catch (error) {
        console.error('خطأ في إنهاء جلسة الكشط:', error);
        return { success: false, error: error.message };
    }
}

/**
 * حفظ البيانات المكشوطة
 * @param {Array} data - البيانات المكشوطة
 * @param {string} format - صيغة الحفظ
 */
async function saveScrapedData(data, format) {
    try {
        let content, filename, mimeType;
        
        switch (format) {
            case 'json':
                content = JSON.stringify(data, null, 2);
                filename = `q2_scraped_data_${Date.now()}.json`;
                mimeType = 'application/json';
                break;
                
            case 'csv':
                content = convertToCSV(data);
                filename = `q2_scraped_data_${Date.now()}.csv`;
                mimeType = 'text/csv';
                break;
                
            case 'excel':
                // للمستقبل: تحويل إلى Excel
                content = JSON.stringify(data, null, 2);
                filename = `q2_scraped_data_${Date.now()}.json`;
                mimeType = 'application/json';
                break;
                
            default:
                throw new Error('صيغة غير مدعومة');
        }
        
        // إنشاء blob وتحميله
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        
        const downloadId = await chrome.downloads.download({
            url: url,
            filename: filename,
            saveAs: true
        });
        
        if (downloadId) {
            showNotification(`تم حفظ ${data.length} عنصر بنجاح`, 'success');
            return { success: true, downloadId: downloadId };
        } else {
            throw new Error('فشل في بدء التحميل');
        }
    } catch (error) {
        console.error('خطأ في حفظ البيانات:', error);
        showNotification('فشل في حفظ البيانات', 'error');
        return { success: false, error: error.message };
    }
}

/**
 * الحصول على إعدادات الإضافة
 */
async function getExtensionSettings() {
    return new Promise((resolve) => {
        chrome.storage.sync.get(null, function(settings) {
            resolve({ success: true, settings: settings });
        });
    });
}

/**
 * تحديث إعدادات الإضافة
 * @param {Object} newSettings - الإعدادات الجديدة
 */
async function updateExtensionSettings(newSettings) {
    return new Promise((resolve) => {
        chrome.storage.sync.set(newSettings, function() {
            extensionSettings = { ...extensionSettings, ...newSettings };
            console.log('تم تحديث الإعدادات:', newSettings);
            resolve({ success: true });
        });
    });
}

/**
 * حفظ جلسة في التخزين المحلي
 * @param {Object} session - بيانات الجلسة
 */
async function saveSessionToStorage(session) {
    return new Promise((resolve) => {
        chrome.storage.local.get(['scrapingSessions'], function(result) {
            const sessions = result.scrapingSessions || [];
            sessions.push(session);
            
            // الاحتفاظ بآخر 50 جلسة فقط
            if (sessions.length > 50) {
                sessions.splice(0, sessions.length - 50);
            }
            
            chrome.storage.local.set({ scrapingSessions: sessions }, function() {
                resolve();
            });
        });
    });
}

// دوال مساعدة

/**
 * توليد معرف جلسة فريد
 * @returns {string} معرف الجلسة
 */
function generateSessionId() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

/**
 * توليد اسم ملف للصورة
 * @param {string} url - رابط الصورة
 * @returns {string} اسم الملف
 */
function generateImageFilename(url) {
    try {
        const urlObj = new URL(url);
        const pathname = urlObj.pathname;
        const filename = pathname.split('/').pop();
        
        if (filename && filename.includes('.')) {
            return `q2_image_${Date.now()}_${filename}`;
        } else {
            return `q2_image_${Date.now()}.jpg`;
        }
    } catch {
        return `q2_image_${Date.now()}.jpg`;
    }
}

/**
 * تحويل البيانات إلى صيغة CSV
 * @param {Array} data - البيانات المراد تحويلها
 * @returns {string} البيانات بصيغة CSV
 */
function convertToCSV(data) {
    if (!data || data.length === 0) return '';
    
    // الحصول على المفاتيح من العنصر الأول
    const headers = Object.keys(data[0]);
    
    // إنشاء صف الرؤوس
    let csv = headers.join(',') + '\n';
    
    // إضافة البيانات
    data.forEach(row => {
        const values = headers.map(header => {
            const value = row[header] || '';
            // تنظيف القيم وإضافة علامات اقتباس إذا لزم الأمر
            return typeof value === 'string' && value.includes(',') 
                ? `"${value.replace(/"/g, '""')}"` 
                : value;
        });
        csv += values.join(',') + '\n';
    });
    
    return csv;
}

/**
 * عرض إشعار للمستخدم
 * @param {string} message - نص الإشعار
 * @param {string} type - نوع الإشعار (success, error, warning, info)
 */
function showNotification(message, type = 'info') {
    if (!extensionSettings.enableNotifications) return;
    
    const iconUrl = getNotificationIcon(type);
    
    chrome.notifications.create({
        type: 'basic',
        iconUrl: iconUrl,
        title: 'Q2 Web Scraper',
        message: message
    });
}

/**
 * الحصول على أيقونة الإشعار حسب النوع
 * @param {string} type - نوع الإشعار
 * @returns {string} مسار الأيقونة
 */
function getNotificationIcon(type) {
    switch (type) {
        case 'success':
            return 'icons/icon48.png';
        case 'error':
            return 'icons/icon48.png';
        case 'warning':
            return 'icons/icon48.png';
        default:
            return 'icons/icon48.png';
    }
}

/**
 * عرض رسالة ترحيب للمستخدمين الجدد
 */
function showWelcomeNotification() {
    showNotification('مرحباً بك في Q2 Web Scraper! انقر على الأيقونة لبدء كشط البيانات.', 'info');
}

/**
 * عرض إشعار التحديث
 * @param {string} previousVersion - الإصدار السابق
 */
function showUpdateNotification(previousVersion) {
    showNotification(`تم تحديث Q2 Web Scraper بنجاح من الإصدار ${previousVersion}`, 'success');
}

/**
 * تحديث الإعدادات عند الحاجة
 * @param {string} previousVersion - الإصدار السابق
 */
function updateSettingsIfNeeded(previousVersion) {
    // هنا يمكن إضافة منطق تحديث الإعدادات عند تغيير الإصدار
    console.log('فحص الحاجة لتحديث الإعدادات...');
}

// معالج إغلاق التبويبات
chrome.tabs.onRemoved.addListener(function(tabId, removeInfo) {
    // إنهاء أي جلسات كشط نشطة في التبويب المغلق
    for (const [sessionId, session] of activeScrapingSessions) {
        if (session.tabId === tabId) {
            endScrapingSession(sessionId);
        }
    }
});

// معالج تحديث التبويبات
chrome.tabs.onUpdated.addListener(function(tabId, changeInfo, tab) {
    if (changeInfo.status === 'complete' && tab.url) {
        // يمكن إضافة منطق تلقائي هنا عند تحميل صفحة جديدة
        console.log('تم تحميل صفحة جديدة:', tab.url);
    }
});

console.log('Q2 Web Scraper Background Script تم تحميله بنجاح');
