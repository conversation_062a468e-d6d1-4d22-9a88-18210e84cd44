# تعليمات التثبيت والاختبار - إضافة Q2 Web Scraper

## متطلبات النظام
- **المتصفح**: Google Chrome الإصدار 88 أو أحدث
- **نظام التشغيل**: Windows, macOS, أو Linux
- **الذاكرة**: 4 GB RAM كحد أدنى
- **مساحة التخزين**: 50 MB مساحة فارغة

## خطوات التثبيت

### 1. تحضير الملفات
تأكد من وجود جميع الملفات التالية في مجلد الإضافة:

```
Q2-Extension/
├── manifest.json
├── popup/
│   ├── popup.html
│   ├── popup.css
│   └── popup.js
├── content/
│   ├── content.js
│   └── content.css
├── background/
│   └── background.js
├── icons/
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
├── README.md
├── دليل_الاستخدام.md
└── تعليمات_التثبيت_والاختبار.md
```

### 2. إنشاء الأيقونات (إذا لم تكن موجودة)
إذا لم تكن الأيقونات متوفرة، يمكنك إنشاؤها أو استخدام أيقونات مؤقتة:

#### الطريقة الأولى: إنشاء أيقونات بسيطة
```html
<!-- يمكن إنشاء أيقونات SVG بسيطة وتحويلها إلى PNG -->
<svg width="128" height="128" xmlns="http://www.w3.org/2000/svg">
  <rect width="128" height="128" fill="#667eea"/>
  <text x="64" y="80" font-family="Arial" font-size="48" 
        fill="white" text-anchor="middle">Q2</text>
</svg>
```

#### الطريقة الثانية: استخدام أيقونات مؤقتة
- يمكن استخدام أي صورة PNG بالأحجام المطلوبة مؤقتاً
- تأكد من تسمية الملفات بالأسماء الصحيحة

### 3. تثبيت الإضافة في Chrome

#### الخطوة 1: فتح صفحة الإضافات
1. افتح متصفح Google Chrome
2. اكتب في شريط العناوين: `chrome://extensions/`
3. اضغط Enter

#### الخطوة 2: تفعيل وضع المطور
1. في الزاوية العلوية اليمنى، فعل "وضع المطور" (Developer mode)
2. ستظهر أزرار إضافية في الأعلى

#### الخطوة 3: تحميل الإضافة
1. انقر على "تحميل إضافة غير مُعبأة" (Load unpacked)
2. اختر مجلد الإضافة الذي يحتوي على ملف `manifest.json`
3. انقر على "اختيار مجلد" أو "Select Folder"

#### الخطوة 4: التحقق من التثبيت
- يجب أن تظهر الإضافة في قائمة الإضافات
- تأكد من أن الحالة "مُفعلة" (Enabled)
- يجب أن تظهر أيقونة Q2 في شريط أدوات Chrome

## اختبار الإضافة

### 1. الاختبار الأساسي

#### اختبار فتح الواجهة:
1. انقر على أيقونة Q2 في شريط الأدوات
2. يجب أن تظهر النافذة المنبثقة
3. تحقق من ظهور جميع التبويبات (الجداول، المنتجات، الصور، الإعدادات)

#### اختبار التبويبات:
1. انقر على كل تبويب
2. تأكد من تغيير المحتوى
3. تحقق من عدم وجود أخطاء في وحدة تحكم المطور

### 2. اختبار كشط الجداول

#### موقع اختبار مُوصى به:
- **Wikipedia**: جداول المعلومات في المقالات
- **مثال**: https://en.wikipedia.org/wiki/List_of_countries_by_population

#### خطوات الاختبار:
1. افتح صفحة تحتوي على جدول
2. انقر على أيقونة Q2
3. اختر تبويب "الجداول"
4. انقر على "فحص الجداول في الصفحة"
5. **النتيجة المتوقعة**: ظهور قائمة بالجداول المكتشفة

#### اختبار استخراج البيانات:
1. اختر جدول من القائمة
2. حدد الأعمدة المطلوبة
3. انقر على "استخراج البيانات"
4. **النتيجة المتوقعة**: ظهور البيانات في منطقة النتائج

### 3. اختبار كشط المنتجات

#### مواقع اختبار مُوصى بها:
- **Amazon**: https://www.amazon.com/s?k=laptop
- **eBay**: https://www.ebay.com/sch/i.html?_nkw=phone
- **AliExpress**: https://www.aliexpress.com/wholesale?SearchText=headphones

#### خطوات الاختبار:
1. افتح صفحة نتائج بحث للمنتجات
2. اختر تبويب "المنتجات"
3. حدد المعلومات المطلوبة (اسم، سعر، إلخ)
4. انقر على "فحص المنتجات"
5. **النتيجة المتوقعة**: ظهور قائمة بالمنتجات المكتشفة

### 4. اختبار كشط الصور

#### مواقع اختبار مُوصى بها:
- **Unsplash**: https://unsplash.com/
- **Pixabay**: https://pixabay.com/
- **أي موقع يحتوي على معرض صور**

#### خطوات الاختبار:
1. افتح صفحة تحتوي على صور متعددة
2. اختر تبويب "الصور"
3. اضبط الحد الأدنى للحجم (مثل 200 بكسل)
4. اختر صيغ الصور المطلوبة
5. انقر على "فحص الصور"
6. **النتيجة المتوقعة**: ظهور قائمة بالصور المكتشفة

### 5. اختبار التصدير

#### اختبار تصدير CSV:
1. بعد كشط أي بيانات
2. انقر على "تحميل" في منطقة النتائج
3. اختر موقع الحفظ
4. **النتيجة المتوقعة**: تحميل ملف CSV يحتوي على البيانات

#### اختبار تصدير JSON:
1. اذهب إلى تبويب "الإعدادات"
2. غير صيغة التصدير إلى JSON
3. كرر عملية الكشط والتحميل
4. **النتيجة المتوقعة**: تحميل ملف JSON

## استكشاف الأخطاء

### الأخطاء الشائعة وحلولها:

#### 1. "فشل في تحميل الإضافة"
**الأسباب المحتملة:**
- ملف manifest.json غير صحيح
- ملفات مفقودة
- أخطاء في بناء الجملة

**الحلول:**
1. تحقق من وجود جميع الملفات المطلوبة
2. افتح وحدة تحكم المطور للتحقق من الأخطاء
3. تأكد من صحة ملف manifest.json

#### 2. "الإضافة لا تظهر في شريط الأدوات"
**الحلول:**
1. تحقق من تفعيل الإضافة في chrome://extensions/
2. أعد تحميل الإضافة
3. أعد تشغيل Chrome

#### 3. "النافذة المنبثقة فارغة أو لا تظهر"
**الحلول:**
1. تحقق من ملفات popup (HTML, CSS, JS)
2. افتح وحدة تحكم المطور للنافذة المنبثقة
3. تحقق من مسارات الملفات في manifest.json

#### 4. "Content Script لا يعمل"
**الحلول:**
1. تحقق من صلاحيات الإضافة
2. أعد تحميل الصفحة المستهدفة
3. تحقق من وحدة تحكم المطور للصفحة

### فحص الأخطاء باستخدام وحدة تحكم المطور:

#### للنافذة المنبثقة:
1. انقر بالزر الأيمن على أيقونة الإضافة
2. اختر "فحص النافذة المنبثقة" (Inspect popup)
3. ابحث عن أخطاء في تبويب Console

#### للصفحة الرئيسية:
1. اضغط F12 لفتح أدوات المطور
2. اذهب إلى تبويب Console
3. ابحث عن رسائل خطأ من Q2 Web Scraper

#### لـ Background Script:
1. اذهب إلى chrome://extensions/
2. انقر على "تفاصيل" للإضافة
3. انقر على "فحص العروض" > "background page"

## اختبارات الأداء

### 1. اختبار الجداول الكبيرة
- جرب جداول تحتوي على أكثر من 1000 صف
- تحقق من سرعة الاستجابة
- راقب استهلاك الذاكرة

### 2. اختبار المواقع المختلفة
- جرب مواقع بلغات مختلفة
- اختبر مواقع بتصميمات متنوعة
- تحقق من التوافق مع المواقع المتجاوبة

### 3. اختبار الحمولة
- كشط كمية كبيرة من البيانات
- اختبار تصدير ملفات كبيرة
- مراقبة استقرار الإضافة

## التحقق من الجودة

### قائمة فحص شاملة:

#### الوظائف الأساسية:
- [ ] فتح النافذة المنبثقة
- [ ] التنقل بين التبويبات
- [ ] فحص الجداول
- [ ] استخراج بيانات الجداول
- [ ] كشط المنتجات
- [ ] كشط الصور
- [ ] تصدير البيانات (CSV, JSON)
- [ ] حفظ الإعدادات

#### الواجهة:
- [ ] النصوص العربية تظهر بشكل صحيح
- [ ] التخطيط يعمل من اليمين لليسار
- [ ] الألوان والتصميم متسقان
- [ ] الأزرار تستجيب للنقر
- [ ] الرسوم المتحركة تعمل بسلاسة

#### الأداء:
- [ ] سرعة استجابة مقبولة (أقل من 3 ثوان)
- [ ] لا توجد تسريبات في الذاكرة
- [ ] استهلاك معقول لموارد النظام
- [ ] استقرار عند الاستخدام المكثف

#### التوافق:
- [ ] يعمل على مواقع مختلفة
- [ ] متوافق مع أحجام شاشة مختلفة
- [ ] يدعم المتصفحات المختلفة (Chrome, Edge)
- [ ] يعمل مع إضافات أخرى

## نشر الإضافة

### التحضير للنشر:
1. **اختبار شامل**: تأكد من اجتياز جميع الاختبارات
2. **تحسين الأداء**: قم بتحسين الكود وتقليل حجم الملفات
3. **إنشاء الأيقونات النهائية**: أيقونات عالية الجودة بجميع الأحجام
4. **كتابة الوصف**: وصف شامل للإضافة باللغتين العربية والإنجليزية
5. **لقطات الشاشة**: صور توضيحية لواجهة الإضافة

### رفع إلى Chrome Web Store:
1. إنشاء حساب مطور في Chrome Web Store
2. تحضير ملف ZIP للإضافة
3. ملء معلومات الإضافة
4. رفع الملفات والصور
5. انتظار المراجعة والموافقة

---

**ملاحظة مهمة**: تأكد من اختبار الإضافة بشكل شامل قبل النشر لضمان تجربة مستخدم ممتازة.
